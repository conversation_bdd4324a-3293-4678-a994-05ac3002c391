"""
FastAPI主应用
"""
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import asyncio
import logging
from typing import Dict, Any

from .models import VideoInput, ApiResponse, ErrorResponse, AnalysisResult
from .barrage_fetcher import barrage_fetcher
from .barrage_analyzer import barrage_analyzer

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="Bilibili弹幕分析器API",
    description="一个用于分析Bilibili视频弹幕的API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/", response_model=ApiResponse)
async def root():
    """根路径"""
    return ApiResponse(
        success=True,
        message="Bilibili弹幕分析器API服务正在运行",
        data={"version": "1.0.0"}
    )


@app.get("/health", response_model=ApiResponse)
async def health_check():
    """健康检查"""
    return ApiResponse(
        success=True,
        message="服务健康",
        data={"status": "healthy"}
    )


@app.post("/api/video/info", response_model=ApiResponse)
async def get_video_info(video_input: VideoInput):
    """
    获取视频基本信息
    """
    try:
        # 提取BV号
        bv_id = barrage_fetcher.extract_bv_from_url(video_input.url_or_bv)
        if not bv_id:
            raise HTTPException(
                status_code=400,
                detail="无法从输入中提取有效的BV号，请检查URL或BV号格式"
            )
        
        # 获取视频信息
        video_info = await barrage_fetcher.get_video_info(bv_id)
        
        return ApiResponse(
            success=True,
            message="获取视频信息成功",
            data={
                "bv_id": bv_id,
                "video_info": video_info
            }
        )
        
    except Exception as e:
        logger.error(f"获取视频信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/barrage/analyze", response_model=ApiResponse)
async def analyze_barrage(video_input: VideoInput):
    """
    分析视频弹幕
    """
    try:
        # 提取BV号
        bv_id = barrage_fetcher.extract_bv_from_url(video_input.url_or_bv)
        if not bv_id:
            raise HTTPException(
                status_code=400,
                detail="无法从输入中提取有效的BV号，请检查URL或BV号格式"
            )
        
        logger.info(f"开始分析视频 {bv_id} 的弹幕")
        
        # 获取视频信息
        video_info = await barrage_fetcher.get_video_info(bv_id)
        logger.info(f"获取视频信息成功: {video_info['title']}")
        
        # 获取弹幕数据
        barrage_data = await barrage_fetcher.get_barrage_data(bv_id, video_input.page)
        logger.info(f"获取弹幕数据成功，共 {len(barrage_data)} 条弹幕")
        
        if not barrage_data:
            return ApiResponse(
                success=True,
                message="该视频暂无弹幕数据",
                data={
                    "bv_id": bv_id,
                    "video_info": video_info,
                    "analysis_result": None
                }
            )
        
        # 预处理弹幕文本
        processed_texts = barrage_fetcher.preprocess_barrage_text(barrage_data)
        logger.info(f"弹幕预处理完成，有效弹幕 {len(processed_texts)} 条")
        
        # 分析弹幕
        analysis_result = barrage_analyzer.generate_analysis_result(
            video_info, barrage_data, processed_texts
        )
        
        logger.info("弹幕分析完成")
        
        return ApiResponse(
            success=True,
            message="弹幕分析完成",
            data={
                "bv_id": bv_id,
                "analysis_result": analysis_result.dict()
            }
        )
        
    except Exception as e:
        logger.error(f"分析弹幕失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/barrage/raw", response_model=ApiResponse)
async def get_raw_barrage(video_input: VideoInput):
    """
    获取原始弹幕数据（用于调试）
    """
    try:
        # 提取BV号
        bv_id = barrage_fetcher.extract_bv_from_url(video_input.url_or_bv)
        if not bv_id:
            raise HTTPException(
                status_code=400,
                detail="无法从输入中提取有效的BV号，请检查URL或BV号格式"
            )
        
        # 获取弹幕数据
        barrage_data = await barrage_fetcher.get_barrage_data(bv_id, video_input.page)
        
        # 限制返回数量（避免数据过大）
        limited_data = barrage_data[:100] if len(barrage_data) > 100 else barrage_data
        
        return ApiResponse(
            success=True,
            message=f"获取原始弹幕数据成功，共 {len(barrage_data)} 条（显示前100条）",
            data={
                "bv_id": bv_id,
                "total_count": len(barrage_data),
                "barrage_data": limited_data
            }
        )
        
    except Exception as e:
        logger.error(f"获取原始弹幕数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            success=False,
            message=exc.detail,
            error_code=str(exc.status_code)
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            success=False,
            message="服务器内部错误",
            error_code="500"
        ).dict()
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
