"""
弹幕分析模块
"""
import jieba
import re
from collections import Counter
from typing import List, Dict, Any, Tuple
from .models import WordFrequency, TimeDistribution, AnalysisResult, VideoInfo


class BarrageAnalyzer:
    """弹幕分析器"""
    
    def __init__(self):
        # 初始化jieba分词
        jieba.initialize()
        
        # 停用词列表
        self.stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '还', '这', '那', '来', '可以', '什么', '这个', '那个', '怎么', '为什么',
            '哈哈', '呵呵', '嘿嘿', '哇', '啊', '呀', '哦', '嗯', '额', '诶', '咦',
            '6666', '666', '233', '2333', '23333', 'www', 'wwww', 'hhh', 'hhhh'
        }
    
    def analyze_word_frequency(self, texts: List[str], top_n: int = 50) -> List[WordFrequency]:
        """
        分析词频
        
        Args:
            texts: 弹幕文本列表
            top_n: 返回前N个高频词
            
        Returns:
            词频统计结果
        """
        if not texts:
            return []
        
        # 合并所有文本
        all_text = ' '.join(texts)
        
        # 分词
        words = jieba.lcut(all_text)
        
        # 过滤停用词和无意义词
        filtered_words = []
        for word in words:
            word = word.strip()
            # 过滤条件
            if (len(word) >= 2 and  # 长度至少2个字符
                word not in self.stop_words and  # 不在停用词表中
                not word.isdigit() and  # 不是纯数字
                not re.match(r'^[^\w\s]+$', word) and  # 不是纯符号
                re.search(r'[\u4e00-\u9fff]', word)):  # 包含中文字符
                filtered_words.append(word)
        
        # 统计词频
        word_counts = Counter(filtered_words)
        total_words = len(filtered_words)
        
        # 生成结果
        result = []
        for word, count in word_counts.most_common(top_n):
            percentage = (count / total_words) * 100 if total_words > 0 else 0
            result.append(WordFrequency(
                word=word,
                count=count,
                percentage=round(percentage, 2)
            ))
        
        return result
    
    def analyze_time_distribution(self, barrage_data: List[Dict[str, Any]], 
                                interval: int = 30) -> List[TimeDistribution]:
        """
        分析弹幕时间分布
        
        Args:
            barrage_data: 弹幕数据列表
            interval: 时间间隔（秒）
            
        Returns:
            时间分布统计结果
        """
        if not barrage_data:
            return []
        
        # 获取最大时间
        max_time = max(barrage['time'] for barrage in barrage_data)
        
        # 创建时间段
        time_segments = {}
        for barrage in barrage_data:
            time_segment = int(barrage['time'] // interval) * interval
            time_segments[time_segment] = time_segments.get(time_segment, 0) + 1
        
        # 生成完整的时间分布（包括0弹幕的时间段）
        result = []
        for i in range(0, int(max_time) + interval, interval):
            count = time_segments.get(i, 0)
            result.append(TimeDistribution(
                time_point=i,
                count=count
            ))
        
        return result
    
    def calculate_barrage_stats(self, barrage_data: List[Dict[str, Any]], 
                              texts: List[str]) -> Dict[str, float]:
        """
        计算弹幕统计信息
        
        Args:
            barrage_data: 弹幕数据列表
            texts: 处理后的弹幕文本列表
            
        Returns:
            统计信息字典
        """
        if not texts:
            return {
                'avg_length': 0.0,
                'density': 0.0
            }
        
        # 平均弹幕长度
        total_length = sum(len(text) for text in texts)
        avg_length = total_length / len(texts)
        
        # 弹幕密度（条/分钟）
        if barrage_data:
            max_time = max(barrage['time'] for barrage in barrage_data)
            duration_minutes = max_time / 60 if max_time > 0 else 1
            density = len(texts) / duration_minutes
        else:
            density = 0.0
        
        return {
            'avg_length': round(avg_length, 2),
            'density': round(density, 2)
        }
    
    def analyze_barrage_sentiment(self, texts: List[str]) -> Dict[str, Any]:
        """
        简单的情感分析（基于关键词）
        
        Args:
            texts: 弹幕文本列表
            
        Returns:
            情感分析结果
        """
        positive_words = {
            '好', '棒', '赞', '牛', '强', '厉害', '优秀', '精彩', '完美', '喜欢',
            '爱', '支持', '加油', '给力', '太好了', '不错', '很棒', '真好'
        }
        
        negative_words = {
            '差', '烂', '垃圾', '无聊', '讨厌', '恶心', '糟糕', '失望', '不好',
            '难看', '难听', '不行', '太差', '什么鬼', '辣鸡'
        }
        
        positive_count = 0
        negative_count = 0
        neutral_count = 0
        
        for text in texts:
            has_positive = any(word in text for word in positive_words)
            has_negative = any(word in text for word in negative_words)
            
            if has_positive and not has_negative:
                positive_count += 1
            elif has_negative and not has_positive:
                negative_count += 1
            else:
                neutral_count += 1
        
        total = len(texts)
        if total == 0:
            return {
                'positive_ratio': 0.0,
                'negative_ratio': 0.0,
                'neutral_ratio': 0.0
            }
        
        return {
            'positive_ratio': round((positive_count / total) * 100, 2),
            'negative_ratio': round((negative_count / total) * 100, 2),
            'neutral_ratio': round((neutral_count / total) * 100, 2)
        }
    
    def generate_analysis_result(self, video_info: Dict[str, Any], 
                               barrage_data: List[Dict[str, Any]], 
                               texts: List[str]) -> AnalysisResult:
        """
        生成完整的分析结果
        
        Args:
            video_info: 视频信息
            barrage_data: 原始弹幕数据
            texts: 处理后的弹幕文本
            
        Returns:
            完整的分析结果
        """
        # 词频分析
        word_frequency = self.analyze_word_frequency(texts)
        
        # 时间分布分析
        time_distribution = self.analyze_time_distribution(barrage_data)
        
        # 统计信息
        stats = self.calculate_barrage_stats(barrage_data, texts)
        
        # 构建结果
        return AnalysisResult(
            video_info=VideoInfo(**video_info),
            total_barrage_count=len(barrage_data),
            processed_barrage_count=len(texts),
            word_frequency=word_frequency,
            time_distribution=time_distribution,
            avg_barrage_length=stats['avg_length'],
            barrage_density=stats['density']
        )


# 创建全局实例
barrage_analyzer = BarrageAnalyzer()
