"""
Bilibili弹幕获取模块
"""
import re
import asyncio
from typing import List, Dict, Any, Optional
from bilibili_api import video, Credential
from bilibili_api.exceptions import ResponseCodeException


class BarrageFetcher:
    """Bilibili弹幕获取器"""
    
    def __init__(self):
        self.credential = None  # 可以后续添加登录凭证
    
    def extract_bv_from_url(self, url_or_bv: str) -> Optional[str]:
        """
        从URL或直接输入中提取BV号
        
        Args:
            url_or_bv: B站视频URL或BV号
            
        Returns:
            BV号，如果无法提取则返回None
        """
        # 如果直接是BV号
        if url_or_bv.startswith('BV'):
            return url_or_bv
        
        # 从URL中提取BV号
        patterns = [
            r'bilibili\.com/video/(BV[a-zA-Z0-9]+)',
            r'b23\.tv/([a-zA-Z0-9]+)',  # 短链接
            r'(BV[a-zA-Z0-9]+)'  # 直接匹配BV号
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url_or_bv)
            if match:
                bv_id = match.group(1)
                if bv_id.startswith('BV'):
                    return bv_id
                # 对于短链接，需要进一步处理
                elif len(bv_id) == 10:  # 短链接通常是10位
                    return f"BV{bv_id}"
        
        return None
    
    async def get_video_info(self, bv_id: str) -> Dict[str, Any]:
        """
        获取视频基本信息
        
        Args:
            bv_id: BV号
            
        Returns:
            视频信息字典
        """
        try:
            v = video.Video(bvid=bv_id, credential=self.credential)
            info = await v.get_info()
            
            return {
                'title': info.get('title', ''),
                'desc': info.get('desc', ''),
                'duration': info.get('duration', 0),
                'view': info.get('stat', {}).get('view', 0),
                'danmaku': info.get('stat', {}).get('danmaku', 0),
                'like': info.get('stat', {}).get('like', 0),
                'coin': info.get('stat', {}).get('coin', 0),
                'favorite': info.get('stat', {}).get('favorite', 0),
                'share': info.get('stat', {}).get('share', 0),
                'owner': info.get('owner', {}).get('name', ''),
                'pubdate': info.get('pubdate', 0)
            }
        except ResponseCodeException as e:
            raise Exception(f"获取视频信息失败: {e}")
        except Exception as e:
            raise Exception(f"获取视频信息时发生错误: {e}")
    
    async def get_barrage_data(self, bv_id: str, page: int = 1) -> List[Dict[str, Any]]:
        """
        获取弹幕数据
        
        Args:
            bv_id: BV号
            page: 分P页码，默认为1
            
        Returns:
            弹幕数据列表
        """
        try:
            v = video.Video(bvid=bv_id, credential=self.credential)
            
            # 获取视频分P信息
            pages = await v.get_pages()
            if page > len(pages):
                raise Exception(f"分P页码超出范围，该视频共有{len(pages)}个分P")
            
            # 获取指定分P的cid
            cid = pages[page - 1]['cid']
            
            # 获取弹幕
            danmaku_list = await v.get_danmaku(cid)
            
            # 处理弹幕数据
            processed_danmaku = []
            for dm in danmaku_list:
                processed_danmaku.append({
                    'time': dm.dm_time,  # 弹幕出现时间（秒）
                    'type': dm.mode,     # 弹幕类型
                    'font_size': dm.font_size,  # 字体大小
                    'color': dm.color,   # 颜色
                    'timestamp': dm.ctime,  # 发送时间戳
                    'pool': dm.pool,     # 弹幕池
                    'uid_hash': dm.uid_crc32,  # 用户ID哈希
                    'text': dm.text      # 弹幕内容
                })
            
            return processed_danmaku
            
        except ResponseCodeException as e:
            raise Exception(f"获取弹幕失败: {e}")
        except Exception as e:
            raise Exception(f"获取弹幕时发生错误: {e}")
    
    def preprocess_barrage_text(self, barrage_list: List[Dict[str, Any]]) -> List[str]:
        """
        预处理弹幕文本
        
        Args:
            barrage_list: 弹幕数据列表
            
        Returns:
            清洗后的弹幕文本列表
        """
        processed_texts = []
        
        for barrage in barrage_list:
            text = barrage.get('text', '').strip()
            
            # 过滤空弹幕
            if not text:
                continue
            
            # 过滤过短的弹幕（可能是无意义的）
            if len(text) < 2:
                continue
            
            # 过滤纯数字或纯符号的弹幕
            if text.isdigit() or not any(c.isalnum() for c in text):
                continue
            
            # 过滤常见的无意义弹幕
            meaningless_patterns = [
                r'^[哈哈哈]+$',
                r'^[呜呜呜]+$',
                r'^[啊啊啊]+$',
                r'^[。。。]+$',
                r'^[？？？]+$',
                r'^[！！！]+$',
                r'^[6666]+$',
                r'^[2333]+$'
            ]
            
            is_meaningless = False
            for pattern in meaningless_patterns:
                if re.match(pattern, text):
                    is_meaningless = True
                    break
            
            if not is_meaningless:
                processed_texts.append(text)
        
        return processed_texts


# 创建全局实例
barrage_fetcher = BarrageFetcher()
