"""
数据模型定义
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field


class VideoInput(BaseModel):
    """视频输入模型"""
    url_or_bv: str = Field(..., description="B站视频URL或BV号")
    page: int = Field(default=1, description="分P页码，默认为1")


class VideoInfo(BaseModel):
    """视频信息模型"""
    title: str = Field(..., description="视频标题")
    desc: str = Field(default="", description="视频描述")
    duration: int = Field(..., description="视频时长（秒）")
    view: int = Field(default=0, description="播放量")
    danmaku: int = Field(default=0, description="弹幕数")
    like: int = Field(default=0, description="点赞数")
    coin: int = Field(default=0, description="投币数")
    favorite: int = Field(default=0, description="收藏数")
    share: int = Field(default=0, description="分享数")
    owner: str = Field(default="", description="UP主名称")
    pubdate: int = Field(default=0, description="发布时间戳")


class BarrageItem(BaseModel):
    """弹幕项模型"""
    time: float = Field(..., description="弹幕出现时间（秒）")
    type: int = Field(..., description="弹幕类型")
    font_size: int = Field(..., description="字体大小")
    color: int = Field(..., description="颜色")
    timestamp: int = Field(..., description="发送时间戳")
    pool: int = Field(..., description="弹幕池")
    uid_hash: str = Field(..., description="用户ID哈希")
    text: str = Field(..., description="弹幕内容")


class WordFrequency(BaseModel):
    """词频统计模型"""
    word: str = Field(..., description="词语")
    count: int = Field(..., description="出现次数")
    percentage: float = Field(..., description="占比")


class TimeDistribution(BaseModel):
    """时间分布模型"""
    time_point: float = Field(..., description="时间点（秒）")
    count: int = Field(..., description="弹幕数量")


class AnalysisResult(BaseModel):
    """分析结果模型"""
    video_info: VideoInfo = Field(..., description="视频信息")
    total_barrage_count: int = Field(..., description="总弹幕数")
    processed_barrage_count: int = Field(..., description="处理后弹幕数")
    word_frequency: List[WordFrequency] = Field(..., description="词频统计")
    time_distribution: List[TimeDistribution] = Field(..., description="时间分布")
    avg_barrage_length: float = Field(..., description="平均弹幕长度")
    barrage_density: float = Field(..., description="弹幕密度（条/分钟）")


class ApiResponse(BaseModel):
    """API响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(default="", description="响应消息")
    data: Optional[Any] = Field(default=None, description="响应数据")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = Field(default=False, description="是否成功")
    message: str = Field(..., description="错误消息")
    error_code: Optional[str] = Field(default=None, description="错误代码")
